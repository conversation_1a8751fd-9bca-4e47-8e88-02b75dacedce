"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import LoginForm from "@/components/LoginForm"
import RegisterForm from "@/components/RegisterForm"
import { AuthProvider, useAuth } from "@/contexts/AuthContext"
import { LogOut, User, Phone, Calendar } from "lucide-react"

function AuthDemoContent() {
  const [showLogin, setShowLogin] = useState(false)
  const [showRegister, setShowRegister] = useState(false)
  const { user, isAuthenticated, logout, isLoading } = useAuth()

  const handleLoginSuccess = (userData: any, token: string) => {
    console.log("Login successful:", userData)
    setShowLogin(false)
  }

  const handleRegisterSuccess = (userData: any, token: string) => {
    console.log("Registration successful:", userData)
    setShowRegister(false)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#F0F7FE] to-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[#00A7E1] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#F0F7FE] to-white">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card className="bg-white shadow-xl border-0 rounded-3xl overflow-hidden">
            <CardHeader className="bg-gradient-to-br from-[#074377] via-[#00A7E1] to-[#80C2F4] text-white p-8">
              <CardTitle className="text-3xl font-black text-center">
                🔐 Demo de Autenticação LocPay
              </CardTitle>
              <p className="text-center text-white/90 mt-2">
                Teste os formulários de login e registro
              </p>
            </CardHeader>

            <CardContent className="p-8">
              {!isAuthenticated ? (
                <div className="space-y-6">
                  <div className="text-center space-y-4">
                    <h2 className="text-2xl font-bold text-[#074377]">
                      Bem-vindo ao LocPay
                    </h2>
                    <p className="text-gray-600">
                      Faça login ou crie uma conta para continuar
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Button
                      onClick={() => setShowLogin(true)}
                      className="h-14 bg-gradient-to-r from-[#00A7E1] to-[#80C2F4] hover:from-[#0096CC] hover:to-[#6BB8F0] text-white font-bold rounded-2xl transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
                    >
                      🔐 Fazer Login
                    </Button>

                    <Button
                      onClick={() => setShowRegister(true)}
                      variant="outline"
                      className="h-14 border-2 border-[#00A7E1] text-[#00A7E1] hover:bg-[#00A7E1] hover:text-white font-bold rounded-2xl transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
                    >
                      📝 Criar Conta
                    </Button>
                  </div>

                  <div className="bg-[#F0F7FE] rounded-2xl p-6 space-y-4">
                    <h3 className="font-bold text-[#074377] text-lg">
                      ℹ️ Como funciona:
                    </h3>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li className="flex items-start gap-2">
                        <span className="text-[#00A7E1] font-bold">1.</span>
                        <span><strong>Login:</strong> Digite apenas seu CPF se já tem conta</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-[#00A7E1] font-bold">2.</span>
                        <span><strong>Registro:</strong> Preencha nome, CPF e telefone para criar conta</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-[#00A7E1] font-bold">3.</span>
                        <span><strong>Verificação:</strong> Receba e digite o código do WhatsApp</span>
                      </li>
                    </ul>
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="text-center space-y-4">
                    <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto">
                      <User className="w-10 h-10 text-white" />
                    </div>
                    <h2 className="text-2xl font-bold text-[#074377]">
                      Bem-vindo, {user?.name}! 👋
                    </h2>
                    <p className="text-gray-600">
                      Você está autenticado com sucesso
                    </p>
                  </div>

                  <div className="bg-[#F0F7FE] rounded-2xl p-6 space-y-4">
                    <h3 className="font-bold text-[#074377] text-lg flex items-center gap-2">
                      <User className="w-5 h-5" />
                      Informações da Conta
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3 text-sm">
                        <User className="w-4 h-4 text-[#00A7E1]" />
                        <span className="font-medium">Nome:</span>
                        <span className="text-gray-600">{user?.name}</span>
                      </div>
                      {user?.phone && (
                        <div className="flex items-center gap-3 text-sm">
                          <Phone className="w-4 h-4 text-[#00A7E1]" />
                          <span className="font-medium">Telefone:</span>
                          <span className="text-gray-600">{user.phone}</span>
                        </div>
                      )}
                      <div className="flex items-center gap-3 text-sm">
                        <Calendar className="w-4 h-4 text-[#00A7E1]" />
                        <span className="font-medium">Membro desde:</span>
                        <span className="text-gray-600">
                          {new Date(user?.createdAt || "").toLocaleDateString("pt-BR")}
                        </span>
                      </div>
                    </div>
                  </div>

                  <Button
                    onClick={logout}
                    variant="outline"
                    className="w-full h-12 border-2 border-red-500 text-red-500 hover:bg-red-500 hover:text-white font-bold rounded-2xl transition-all duration-300 hover:scale-[1.02] hover:shadow-lg flex items-center gap-2"
                  >
                    <LogOut className="w-4 h-4" />
                    Sair da Conta
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Login Modal */}
      <LoginForm
        isOpen={showLogin}
        onClose={() => setShowLogin(false)}
        onLoginSuccess={handleLoginSuccess}
      />

      {/* Register Modal */}
      <RegisterForm
        isOpen={showRegister}
        onClose={() => setShowRegister(false)}
        onRegisterSuccess={handleRegisterSuccess}
      />
    </div>
  )
}

export default function AuthDemoPage() {
  return (
    <AuthProvider>
      <AuthDemoContent />
    </AuthProvider>
  )
}
