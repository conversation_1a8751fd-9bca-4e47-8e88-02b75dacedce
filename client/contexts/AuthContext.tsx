"use client"

import React, { createContext, useContext, useState, useEffect } from "react"

interface User {
  id: string
  name: string
  phone?: string
  createdAt: string
}

interface AuthContextType {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (user: User, token: string) => void
  logout: () => void
  checkAuthStatus: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user && !!token

  // Load auth data from localStorage on mount
  useEffect(() => {
    const savedToken = localStorage.getItem("locpay_token")
    const savedUser = localStorage.getItem("locpay_user")

    if (savedToken && savedUser) {
      try {
        const parsedUser = JSON.parse(savedUser)
        setToken(savedToken)
        setUser(parsedUser)
        // Optionally validate token with backend
        checkAuthStatus()
      } catch (error) {
        console.error("Error parsing saved user data:", error)
        logout()
      }
    } else {
      setIsLoading(false)
    }
  }, [])

  const login = (userData: User, authToken: string) => {
    setUser(userData)
    setToken(authToken)
    localStorage.setItem("locpay_token", authToken)
    localStorage.setItem("locpay_user", JSON.stringify(userData))
    setIsLoading(false)
  }

  const logout = () => {
    setUser(null)
    setToken(null)
    localStorage.removeItem("locpay_token")
    localStorage.removeItem("locpay_user")
    setIsLoading(false)
  }

  const checkAuthStatus = async () => {
    const savedToken = localStorage.getItem("locpay_token")
    
    if (!savedToken) {
      setIsLoading(false)
      return
    }

    try {
      const response = await fetch("/api/v1/auth/validate-token", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token: savedToken }),
      })

      const result = await response.json()

      if (response.ok && result.success && result.data.valid) {
        // Token is valid, update user data if provided
        if (result.data.user) {
          setUser(result.data.user)
          localStorage.setItem("locpay_user", JSON.stringify(result.data.user))
        }
        setToken(savedToken)
      } else {
        // Token is invalid, logout
        logout()
      }
    } catch (error) {
      console.error("Error validating token:", error)
      // On network error, keep user logged in but mark as not loading
      // This prevents logout on temporary network issues
    } finally {
      setIsLoading(false)
    }
  }

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated,
    isLoading,
    login,
    logout,
    checkAuthStatus,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
