"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import LoginForm from "@/components/LoginForm"
import RegisterForm from "@/components/RegisterForm"
import { useAuth } from "@/contexts/AuthContext"
import { LogOut, User } from "lucide-react"

export default function AuthButtons() {
  const [showLogin, setShowLogin] = useState(false)
  const [showRegister, setShowRegister] = useState(false)
  const { user, isAuthenticated, logout } = useAuth()

  const handleLoginSuccess = (userData: any, token: string) => {
    console.log("Login successful:", userData)
    setShowLogin(false)
  }

  const handleRegisterSuccess = (userData: any, token: string) => {
    console.log("Registration successful:", userData)
    setShowRegister(false)
  }

  if (isAuthenticated && user) {
    return (
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <User className="w-4 h-4" />
          <span><PERSON><PERSON><PERSON>, {user.name}</span>
        </div>
        <Button
          onClick={logout}
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <LogOut className="w-4 h-4" />
          Sair
        </Button>
      </div>
    )
  }

  return (
    <>
      <div className="flex items-center gap-2">
        <Button
          onClick={() => setShowLogin(true)}
          variant="ghost"
          size="sm"
          className="text-[#074377] hover:text-[#00A7E1]"
        >
          Entrar
        </Button>
        <Button
          onClick={() => setShowRegister(true)}
          size="sm"
          className="bg-gradient-to-r from-[#00A7E1] to-[#80C2F4] hover:from-[#0096CC] hover:to-[#6BB8F0] text-white"
        >
          Criar Conta
        </Button>
      </div>

      {/* Login Modal */}
      <LoginForm
        isOpen={showLogin}
        onClose={() => setShowLogin(false)}
        onLoginSuccess={handleLoginSuccess}
      />

      {/* Register Modal */}
      <RegisterForm
        isOpen={showRegister}
        onClose={() => setShowRegister(false)}
        onRegisterSuccess={handleRegisterSuccess}
      />
    </>
  )
}
