"use client"

import type React from "react"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { X, CheckCircle, AlertCircle, Lock, Smartphone, ArrowLeft } from "lucide-react"
import Image from "next/image"

interface LoginFormData {
  cpf: string
  code: string
}

interface LoginFormProps {
  isOpen: boolean
  onClose: () => void
  onLoginSuccess?: (user: any, token: string) => void
}

type FormStep = "cpf" | "verification" | "loading" | "success" | "error"

export default function LoginForm({ isOpen, onClose, onLoginSuccess }: LoginFormProps) {
  const [formData, setFormData] = useState<LoginFormData>({
    cpf: "",
    code: "",
  })
  
  const [step, setStep] = useState<FormStep>("cpf")
  const [errors, setErrors] = useState<{ cpf?: string; code?: string }>({})
  const [errorMessage, setErrorMessage] = useState("")

  const formatCPF = (value: string) => {
    const numbers = value.replace(/\D/g, "")
    return numbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4")
  }

  const handleInputChange = (field: keyof LoginFormData, value: string) => {
    if (field === "cpf") {
      const formatted = formatCPF(value)
      if (formatted.length <= 14) {
        setFormData(prev => ({ ...prev, [field]: formatted }))
        if (errors[field]) {
          setErrors(prev => ({ ...prev, [field]: undefined }))
        }
      }
    } else if (field === "code") {
      const numbers = value.replace(/\D/g, "")
      if (numbers.length <= 6) {
        setFormData(prev => ({ ...prev, [field]: numbers }))
        if (errors[field]) {
          setErrors(prev => ({ ...prev, [field]: undefined }))
        }
      }
    }
  }

  const validateCPF = () => {
    const cpfNumbers = formData.cpf.replace(/\D/g, "")
    if (cpfNumbers.length !== 11) {
      setErrors(prev => ({ ...prev, cpf: "CPF deve ter 11 dígitos" }))
      return false
    }
    return true
  }

  const validateCode = () => {
    if (formData.code.length !== 6) {
      setErrors(prev => ({ ...prev, code: "Código deve ter 6 dígitos" }))
      return false
    }
    return true
  }

  const handleCPFSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateCPF()) return

    setStep("loading")
    setErrorMessage("")

    try {
      const response = await fetch("/api/v1/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cpf: formData.cpf.replace(/\D/g, ""),
        }),
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setStep("verification")
      } else {
        setErrorMessage(result.message || "Erro ao enviar código. Tente novamente.")
        setStep("error")
      }
    } catch (error) {
      console.error("Erro ao fazer login:", error)
      setErrorMessage("Erro de conexão. Verifique sua internet e tente novamente.")
      setStep("error")
    }
  }

  const handleCodeSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateCode()) return

    setStep("loading")
    setErrorMessage("")

    try {
      const response = await fetch("/api/v1/auth/verify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cpf: formData.cpf.replace(/\D/g, ""),
          code: formData.code,
        }),
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setStep("success")
        if (onLoginSuccess) {
          onLoginSuccess(result.data.user, result.data.accessToken)
        }
        // Reset form after success
        setTimeout(() => {
          setFormData({ cpf: "", code: "" })
          setStep("cpf")
          onClose()
        }, 2000)
      } else {
        setErrorMessage(result.message || "Código inválido. Tente novamente.")
        setStep("verification")
      }
    } catch (error) {
      console.error("Erro ao verificar código:", error)
      setErrorMessage("Erro de conexão. Verifique sua internet e tente novamente.")
      setStep("verification")
    }
  }

  const handleResendCode = async () => {
    try {
      const response = await fetch("/api/v1/auth/resend-code", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cpf: formData.cpf.replace(/\D/g, ""),
        }),
      })

      const result = await response.json()
      if (response.ok && result.success) {
        // Show success message briefly
        setErrorMessage("")
      } else {
        setErrorMessage("Erro ao reenviar código. Tente novamente.")
      }
    } catch (error) {
      console.error("Erro ao reenviar código:", error)
      setErrorMessage("Erro ao reenviar código. Tente novamente.")
    }
  }

  const resetForm = () => {
    setFormData({ cpf: "", code: "" })
    setErrors({})
    setErrorMessage("")
    setStep("cpf")
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md mx-auto bg-white shadow-2xl border-0 rounded-3xl overflow-hidden">
        {/* Header */}
        <CardHeader className="relative bg-gradient-to-br from-[#074377] via-[#00A7E1] to-[#80C2F4] text-white p-6">
          <Button
            onClick={handleClose}
            variant="ghost"
            size="icon"
            className="absolute top-4 right-4 text-white hover:bg-white/20 rounded-full"
          >
            <X className="w-5 h-5" />
          </Button>

          <div className="text-center space-y-4">
            {/* Logo */}
            <div className="flex justify-center mb-4">
              <div className="relative">
                <div className="absolute inset-0 bg-[#003366] rounded-xl blur-sm"></div>
                <div className="relative bg-[#003366] backdrop-blur-sm rounded-xl px-4 py-2 border border-[#074377]">
                  <Image
                    src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Marca%20d'agua-52nAtl2sZqQVf7puMShdyljk3DsiXv.png"
                    alt="LocPay"
                    width={120}
                    height={40}
                    className="h-8 w-auto"
                  />
                </div>
              </div>
            </div>

            <CardTitle className="text-2xl md:text-3xl font-black leading-tight tracking-tight">
              {step === "cpf" && "🔐 Fazer Login"}
              {step === "verification" && "📱 Verificação WhatsApp"}
              {step === "loading" && "⏳ Processando..."}
              {step === "success" && "✅ Login Realizado!"}
              {step === "error" && "❌ Erro no Login"}
            </CardTitle>
          </div>
        </CardHeader>

        {/* Content */}
        <CardContent className="px-6 pb-6 bg-gradient-to-br from-white/90 to-[#F0F7FE]/50">
          {/* CPF Step */}
          {step === "cpf" && (
            <form onSubmit={handleCPFSubmit} className="space-y-6 pt-6">
              <div className="space-y-2">
                <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                  <Lock className="w-4 h-4" />
                  <span>CPF</span>
                  <span className="text-red-500">*</span>
                </label>
                <Input
                  type="text"
                  placeholder="000.000.000-00"
                  value={formData.cpf}
                  onChange={(e) => handleInputChange("cpf", e.target.value)}
                  className={`w-full h-12 px-4 text-sm bg-white border-2 rounded-2xl transition-all duration-300 focus:bg-white focus:scale-[1.02] placeholder:text-gray-400 ${
                    errors.cpf
                      ? "border-red-500 focus:border-red-500 shadow-red-100"
                      : "border-gray-200 focus:border-[#00A7E1] hover:border-[#80C2F4] focus:shadow-[#00A7E1]/20"
                  } focus:ring-0 focus:outline-none focus:shadow-lg`}
                  maxLength={14}
                />
                {errors.cpf && (
                  <p className="text-red-500 text-xs flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {errors.cpf}
                  </p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full h-12 bg-gradient-to-r from-[#00A7E1] to-[#80C2F4] hover:from-[#0096CC] hover:to-[#6BB8F0] text-white font-bold rounded-2xl transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
              >
                Continuar
              </Button>

              <p className="text-xs text-gray-500 text-center">
                Enviaremos um código de verificação para seu WhatsApp cadastrado
              </p>
            </form>
          )}

          {/* Verification Step */}
          {step === "verification" && (
            <form onSubmit={handleCodeSubmit} className="space-y-6 pt-6">
              <div className="text-center space-y-2 mb-6">
                <Smartphone className="w-12 h-12 text-[#00A7E1] mx-auto" />
                <p className="text-sm text-gray-600">
                  Código enviado para seu WhatsApp
                </p>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                  <span>Código de Verificação</span>
                  <span className="text-red-500">*</span>
                </label>
                <Input
                  type="text"
                  placeholder="000000"
                  value={formData.code}
                  onChange={(e) => handleInputChange("code", e.target.value)}
                  className={`w-full h-12 px-4 text-sm bg-white border-2 rounded-2xl transition-all duration-300 focus:bg-white focus:scale-[1.02] placeholder:text-gray-400 text-center text-lg tracking-widest ${
                    errors.code
                      ? "border-red-500 focus:border-red-500 shadow-red-100"
                      : "border-gray-200 focus:border-[#00A7E1] hover:border-[#80C2F4] focus:shadow-[#00A7E1]/20"
                  } focus:ring-0 focus:outline-none focus:shadow-lg`}
                  maxLength={6}
                />
                {errors.code && (
                  <p className="text-red-500 text-xs flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {errors.code}
                  </p>
                )}
              </div>

              <div className="space-y-3">
                <Button
                  type="submit"
                  className="w-full h-12 bg-gradient-to-r from-[#00A7E1] to-[#80C2F4] hover:from-[#0096CC] hover:to-[#6BB8F0] text-white font-bold rounded-2xl transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
                >
                  Verificar Código
                </Button>

                <div className="flex items-center justify-between">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={() => setStep("cpf")}
                    className="text-gray-500 hover:text-[#074377] flex items-center gap-1"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    Voltar
                  </Button>

                  <Button
                    type="button"
                    variant="ghost"
                    onClick={handleResendCode}
                    className="text-[#00A7E1] hover:text-[#074377]"
                  >
                    Reenviar código
                  </Button>
                </div>
              </div>
            </form>
          )}

          {/* Loading State */}
          {step === "loading" && (
            <div className="text-center py-8">
              <div className="w-16 h-16 border-4 border-[#00A7E1] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-600">Processando...</p>
            </div>
          )}

          {/* Success State */}
          {step === "success" && (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-[#074377] mb-2">Login realizado com sucesso!</h3>
              <p className="text-gray-600">Redirecionando...</p>
            </div>
          )}

          {/* Error State */}
          {step === "error" && (
            <div className="space-y-6 pt-6">
              <div className="text-center">
                <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-[#074377] mb-2">Erro no login</h3>
                <p className="text-red-600 text-sm">{errorMessage}</p>
              </div>

              <Button
                onClick={() => setStep("cpf")}
                className="w-full h-12 bg-gradient-to-r from-[#00A7E1] to-[#80C2F4] hover:from-[#0096CC] hover:to-[#6BB8F0] text-white font-bold rounded-2xl transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
              >
                Tentar Novamente
              </Button>
            </div>
          )}

          {/* Error Message for verification step */}
          {step === "verification" && errorMessage && (
            <div className="mt-4 p-3 bg-red-50 border-l-4 border-red-500 rounded-r-2xl">
              <p className="text-red-700 text-sm flex items-center gap-2">
                <AlertCircle className="w-4 h-4" />
                {errorMessage}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
