"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import Image from "next/image"
import { Menu, Phone, X, Instagram, Gift, MessageSquare, BookOpen } from "lucide-react"
import { Button } from "@/components/ui/button"
import AuthButtons from "@/components/AuthButtons"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const pathname = usePathname()

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen)

  const closeMenu = () => setIsMenuOpen(false)

  const handleSectionClick = (sectionId: string) => {
    closeMenu()
    if (pathname !== "/") {
      window.location.href = `/#${sectionId}`
    } else {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({ behavior: "smooth" })
      }
    }
  }

  return (
    <header className="sticky top-0 z-50 bg-[#004B87] text-white">
      <div className="container mx-auto px-4 h-14 sm:h-16 flex items-center justify-between">
        <Link href="/" className="flex items-center">
          <div className="rounded-lg p-1.5">
            <Image
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Marca%20d'agua-52nAtl2sZqQVf7puMShdyljk3DsiXv.png"
              alt="LocPay"
              width={90}
              height={30}
              className="h-4 w-auto sm:h-6"
            />
          </div>
        </Link>

        {/* Mobile Menu Button */}
        {isMobile && (
          <Button className="md:hidden p-1.5" variant="ghost" size="icon" onClick={toggleMenu}>
            {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            <span className="sr-only">{isMenuOpen ? "Fechar menu" : "Abrir menu"}</span>
          </Button>
        )}

        {/* Desktop Navigation */}
        {!isMobile && (
          <nav className="hidden md:flex items-center space-x-12">
            <button
              onClick={() => handleSectionClick("vantagens")}
              className="text-white font-bold hover:text-[#6BBAED] text-sm flex items-center gap-2"
            >
              <Gift className="h-4 w-4" />
              Benefícios
            </button>
            <button
              onClick={() => handleSectionClick("depoimentos")}
              className="text-white font-bold hover:text-[#6BBAED] text-sm flex items-center gap-2"
            >
              <MessageSquare className="h-4 w-4" />
              Depoimentos
            </button>
            <Link href="/blog" className="text-white font-bold hover:text-[#6BBAED] text-sm flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Blog
            </Link>
            <Link
              href="https://api.whatsapp.com/send/?phone=%2B5585991992305&text=Vim+pelo+site+e+quero+transformar+meus+alugueis+futuros+em+dinheiro+agora+por+meio+da+LocPay%21&type=phone_number&app_absent=0"
              target="_blank"
              rel="noopener noreferrer"
              className="text-white font-bold hover:text-[#6BBAED] text-sm flex items-center gap-2"
            >
              <Phone className="h-4 w-4" />
              Contato
            </Link>
          </nav>
        )}

        {/* Desktop CTA and Social Icons */}
        {!isMobile && (
          <div className="hidden md:flex items-center space-x-4">
            <AuthButtons />
            <div className="flex items-center space-x-4">
              <Link
                href="https://api.whatsapp.com/send/?phone=%2B5585991992305&text=Vim+pelo+site+e+quero+transformar+meus+alugueis+futuros+em+dinheiro+agora+por+meio+da+LocPay%21&type=phone_number&app_absent=0"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-[#6BBAED] transition-colors"
              >
                <Phone className="h-5 w-5" />
              </Link>
              <Link
                href="https://www.instagram.com/locpaybr/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-[#6BBAED] transition-colors"
              >
                <Instagram className="h-5 w-5" />
              </Link>
            </div>
          </div>
        )}

        {/* Mobile Menu */}
        {isMobile && (
          <div
            className={`fixed inset-0 bg-[#004B87] z-50 transform transition-transform duration-300 ease-in-out ${
              isMenuOpen ? "translate-x-0" : "translate-x-full"
            }`}
          >
            <div className="flex flex-col h-full p-4">
              <div className="flex justify-between items-center mb-8">
                <Link href="/" onClick={closeMenu}>
                  <Image
                    src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Marca%20d'agua-52nAtl2sZqQVf7puMShdyljk3DsiXv.png"
                    alt="LocPay"
                    width={90}
                    height={30}
                    className="h-6 w-auto"
                  />
                </Link>
                <Button variant="ghost" size="icon" onClick={closeMenu}>
                  <X className="h-6 w-6" />
                </Button>
              </div>
              <nav className="flex flex-col space-y-6">
                <button
                  onClick={() => handleSectionClick("vantagens")}
                  className="text-white font-bold hover:text-[#6BBAED] text-lg flex items-center gap-2"
                >
                  <Gift className="h-5 w-5" />
                  Benefícios
                </button>
                <button
                  onClick={() => handleSectionClick("depoimentos")}
                  className="text-white font-bold hover:text-[#6BBAED] text-lg flex items-center gap-2"
                >
                  <MessageSquare className="h-5 w-5" />
                  Depoimentos
                </button>
                <Link
                  href="/blog"
                  className="text-white font-bold hover:text-[#6BBAED] text-lg flex items-center gap-2"
                  onClick={closeMenu}
                >
                  <BookOpen className="h-5 w-5" />
                  Blog
                </Link>
                <Link
                  href="https://api.whatsapp.com/send/?phone=%2B5585991992305&text=Vim+pelo+site+e+quero+transformar+meus+alugueis+futuros+em+dinheiro+agora+por+meio+da+LocPay%21&type=phone_number&app_absent=0"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-white font-bold hover:text-[#6BBAED] text-lg flex items-center gap-2"
                  onClick={closeMenu}
                >
                  <Phone className="h-5 w-5" />
                  Contato
                </Link>
              </nav>
              <div className="mt-auto">
                <div className="mb-6">
                  <AuthButtons />
                </div>
                <div className="flex justify-center space-x-8 mb-4">
                  <Link
                    href="https://api.whatsapp.com/send/?phone=%2B5585991992305&text=Vim+pelo+site+e+quero+transformar+meus+alugueis+futuros+em+dinheiro+agora+por+meio+da+LocPay%21&type=phone_number&app_absent=0"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white hover:text-[#6BBAED] transition-colors"
                    onClick={closeMenu}
                  >
                    <Phone className="h-6 w-6" />
                  </Link>
                  <Link
                    href="https://www.instagram.com/locpaybr/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white hover:text-[#6BBAED] transition-colors"
                    onClick={closeMenu}
                  >
                    <Instagram className="h-6 w-6" />
                  </Link>
                </div>
                <Link
                  href="https://api.whatsapp.com/send/?phone=%2B5585991992305&text=Vim+pelo+site+e+quero+transformar+meus+alugueis+futuros+em+dinheiro+agora+por+meio+da+LocPay%21&type=phone_number&app_absent=0"
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={closeMenu}
                >
                  <Button className="w-full bg-white text-[#004B87] hover:bg-[#6BBAED] hover:text-white font-bold text-sm rounded-full px-6 py-3">
                    Quero Receber 1 Ano de Aluguel Agora
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
