"use client"

import type React from "react"
import { useState, useEffect, useRef, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import {
  Check,
  AlertTriangle,
  Settings2,
  X,
  ChevronLeft,
  CreditCard,
  Smartphone,
  Mail,
  Smile,
  CalendarDays,
  Zap,
  Clock,
  Sparkles,
  Eye,
  Upload,
  XCircle,
  ThumbsDown,
  FileText,
  MapPin,
  Building,
  Calendar,
  DollarSign,
  MessageCircle,
  Shield,
  Star,
  Award,
  ArrowRight,
  CheckCircle,
  Rocket,
  CheckCircle2,
  Phone,
  TrendingUp,
  RefreshCw,
  HeartHandshake,
} from "lucide-react"
import Image from "next/image"

// --- Tipos e Estado Inicial ---
interface FormData {
  nomeCompleto: string
  cpf: string
  celular: string
  email: string
  mesesAntecipacao: string
  contratoFileName: string
  dataConsent: boolean
}

interface AppState {
  step: number
  loading: boolean
  loadingMsg: string
  formData: FormData
  proposalMonths: number
  error: string
  extractedData: any
  showRejectDialog: boolean
}

const INITIAL_STATE: AppState = {
  step: 0,
  loading: false,
  loadingMsg: "",
  formData: {
    nomeCompleto: "",
    cpf: "",
    celular: "",
    email: "",
    mesesAntecipacao: "",
    contratoFileName: "",
    dataConsent: false,
  },
  proposalMonths: 6,
  error: "",
  extractedData: {},
  showRejectDialog: false,
}

// --- Dados Mockados para Simulação ---
const MOCK_EXTRACTED_DATA = {
  Inquilino: "João da Silva Santos",
  Proprietário: "Maria Oliveira Costa",
  Imóvel: "Rua das Palmeiras, 456, São Paulo/SP",
  "Valor do Aluguel": "R$ 2.800,00",
  "Vigência do Contrato": "01/01/2024 - 31/12/2026",
  Imobiliária: "Imobiliária Alfa Ltda",
}

// --- Componente Principal ---
export default function LocPayApp() {
  const [state, setState] = useState<AppState>(INITIAL_STATE)
  const firstInputRef = useRef<HTMLInputElement>(null)
  const [showQuickNav, setShowQuickNav] = useState(false)

  const maskCPF = useCallback(
    (value: string) =>
      value
        .replace(/\D/g, "")
        .replace(/(\d{3})(\d)/, "$1.$2")
        .replace(/(\d{3})(\d)/, "$1.$2")
        .replace(/(\d{3})(\d{1,2})/, "$1-$2")
        .replace(/(-\d{2})\d+?$/, "$1"),
    [],
  )

  const maskPhone = useCallback(
    (value: string) =>
      value
        .replace(/\D/g, "")
        .replace(/(\d{2})(\d)/, "($1) $2")
        .replace(/(\d{5})(\d)/, "$1-$2")
        .replace(/(-\d{4})\d+?$/, "$1"),
    [],
  )

  const formatCurrency = (value: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(value)

  const startLoading = useCallback((min: number, max: number, msg: string, onComplete: () => void) => {
    setState((prev) => ({ ...prev, loading: true, loadingMsg: msg, error: "" }))
    const duration = Math.random() * (max - min) + min
    setTimeout(() => {
      onComplete()
      setState((prev) => ({ ...prev, loading: false, loadingMsg: "" }))
      setTimeout(() => firstInputRef.current?.focus(), 100)
    }, duration)
  }, [])

  useEffect(() => {
    try {
      const savedState = localStorage.getItem("locpay_app_state_v1")
      if (savedState) {
        const parsed = JSON.parse(savedState)
        setState((prev) => ({ ...prev, ...parsed, loading: false }))
      }
    } catch (e) {
      console.error("Failed to load state", e)
    }
  }, [])

  useEffect(() => {
    localStorage.setItem("locpay_app_state_v1", JSON.stringify(state))
  }, [state])

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" })
  }, [state.step])

  const handlePreviousStep = () => {
    setState((prev) => {
      if (prev.step > 1) return { ...prev, step: prev.step - 1, error: "" }
      return prev
    })
  }

  const QuickNavigation = () => (
    <Card className="my-4 bg-white shadow-xl rounded-3xl border border-gray-200">
      <CardHeader className="pb-2 pt-4">
        <div className="flex justify-between items-center">
          <CardTitle className="text-sm text-[#074377] flex items-center font-bold">
            <Settings2 className="h-4 w-4 mr-2 text-[#074377]" />
            Navegação Rápida
          </CardTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowQuickNav(false)}
            className="text-[#074377] hover:bg-[#074377]/10 h-8 w-8 rounded-full"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pb-4">
        <div className="flex flex-wrap gap-2 justify-center">
          {[
            { step: 0, label: "Início" },
            { step: 1, label: "Cadastro" },
            { step: 2, label: "Validação" },
            { step: 3, label: "Proposta" },
            { step: 4, label: "Confirmação" },
            { step: 5, label: "Sucesso" },
          ].map((navItem) => (
            <Button
              key={navItem.step}
              onClick={() =>
                setState((prev) => ({
                  ...prev,
                  step: navItem.step,
                  loading: false,
                  error: "",
                }))
              }
              variant={state.step === navItem.step ? "default" : "outline"}
              size="sm"
              className={`${
                state.step === navItem.step
                  ? "bg-[#074377] hover:bg-[#074377]/90 text-white text-xs font-bold"
                  : "border-[#074377] text-[#074377] hover:bg-[#074377]/10 text-xs font-medium"
              } rounded-xl transition-all duration-300`}
            >
              {navItem.label}
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )

  const StepIndicator = () => {
    const stepsConfig = [
      { number: 1, label: "Cadastro" },
      { number: 2, label: "Validação" },
      { number: 3, label: "Proposta" },
      { number: 4, label: "Confirmação" },
    ]
    const currentStep = state.step

    if (currentStep === 0) return null

    return (
      <div className="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-t border-gray-200 py-4 px-4 z-40 shadow-lg">
        <div className="max-w-md mx-auto">
          <div className="flex items-center justify-between relative">
            <div className="absolute top-4 left-8 right-8 h-0.5 bg-gray-200 rounded-full"></div>
            <div
              className="absolute top-4 left-8 h-0.5 bg-gradient-to-r from-[#074377] to-[#0066cc] rounded-full transition-all duration-700 ease-out"
              style={{
                width: `${Math.max(0, ((currentStep - 1) / (stepsConfig.length - 1)) * 100)}%`,
                maxWidth: "calc(100% - 4rem)",
              }}
            ></div>

            {stepsConfig.map((stepItem, index) => (
              <div key={stepItem.number} className="flex flex-col items-center relative z-10">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold border-2 transition-all duration-500 ease-out ${
                    currentStep > stepItem.number
                      ? "bg-gradient-to-r from-[#074377] to-[#0066cc] text-white border-[#074377] scale-110"
                      : currentStep === stepItem.number
                      ? "bg-gradient-to-r from-[#074377] to-[#0066cc] text-white border-[#074377] scale-110 shadow-lg"
                      : "bg-white text-gray-400 border-gray-300"
                  }`}
                >
                  {currentStep > stepItem.number ? <Check className="h-4 w-4" /> : stepItem.number}
                </div>
                <span
                  className={`text-xs mt-2 font-medium transition-all duration-300 ${
                    currentStep >= stepItem.number ? "text-[#074377]" : "text-gray-400"
                  }`}
                >
                  {stepItem.label}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const LoadingScreen = () => (
    <div className="fixed inset-0 bg-gradient-to-br from-[#074377]/95 to-[#0066cc]/95 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="text-center p-8 bg-white/95 backdrop-blur-md rounded-3xl shadow-2xl max-w-sm mx-4 border border-white/20">
        <div className="relative mb-8">
          <div className="w-24 h-24 border-4 border-gray-200 rounded-full animate-spin mx-auto">
            <div className="w-full h-full border-4 border-transparent border-t-[#074377] border-r-[#0066cc] rounded-full animate-spin"></div>
          </div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-12 h-12 bg-gradient-to-r from-[#074377] to-[#0066cc] rounded-full flex items-center justify-center shadow-lg">
              <Check className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>
        <div className="space-y-4">
          <p className="text-[#074377] font-bold text-xl mb-2" aria-live="polite">
            {state.loadingMsg}
          </p>
          <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
            <Clock className="w-4 h-4" />
            <span>Processando com IA...</span>
          </div>
        </div>
      </div>
    </div>
  )

  const commonInputClass =
    "h-12 bg-white border-2 border-gray-300 focus:border-[#074377] focus:ring-2 focus:ring-[#074377]/20 rounded-xl text-base text-gray-900 placeholder:text-gray-500 transition-all duration-300 shadow-sm hover:border-gray-400"
  const inputWithIconClass = "pl-12 pr-4"
  const mainButtonClass =
    "w-full h-14 text-base font-semibold bg-gradient-to-r from-[#074377] to-[#0066cc] hover:from-[#074377]/90 hover:to-[#0066cc]/90 text-white transition-all duration-300 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
  const greenButtonClass =
    "w-full h-14 text-base font-semibold bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white transition-all duration-300 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
  const redButtonClass =
    "w-full h-12 text-sm font-medium bg-red-50 border-2 border-red-300 text-red-600 hover:bg-red-100 hover:border-red-400 transition-all duration-300 rounded-xl"

  const PageHeader = ({ showBackArrow = true }: { showBackArrow?: boolean }) => {
    if (state.step === 0) return null

    return (
      <div className="flex items-center justify-between mb-8 px-4 pt-6">
        {showBackArrow && state.step > 1 ? (
          <Button
            variant="ghost"
            size="icon"
            onClick={handlePreviousStep}
            className="text-white hover:bg-white/20 w-10 h-10 rounded-xl transition-all duration-300"
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>
        ) : (
          <div className="w-10 h-10" />
        )}
        <div className="flex justify-center">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400/30 to-blue-600/30 rounded-full blur-sm opacity-40"></div>
            <div className="relative bg-gradient-to-r from-[#074377] to-[#0066cc] rounded-full p-4 border-2 border-white/20 shadow-lg">
              <Image src="/images/locpay-logo.png" alt="LocPay" width={120} height={32} className="h-8 w-auto" />
            </div>
          </div>
        </div>
        <div className="w-10 h-10" />
      </div>
    )
  }

  const InputField = ({
    icon: Icon,
    id,
    placeholder,
    value,
    onChange,
    maxLength,
    type = "text",
    inputRef,
    label,
  }: {
    icon?: React.ComponentType<any>
    id: string
    placeholder: string
    value: string
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
    maxLength?: number
    type?: string
    inputRef?: React.RefObject<HTMLInputElement>
    label?: string
  }) => (
    <div className="space-y-2">
      {label && <label htmlFor={id} className="block text-sm font-medium text-gray-700">{label}</label>}
      <div className="relative">
        {Icon && <Icon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[#074377]" />}
        <Input
          ref={inputRef}
          id={id}
          type={type}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          maxLength={maxLength}
          className={`${commonInputClass} ${Icon ? inputWithIconClass : "px-4"}`}
        />
      </div>
    </div>
  )

  const SecurityBadge = () => (
    <div className="flex items-center justify-center gap-2 mt-6 p-3 bg-gray-50 rounded-xl border border-gray-200">
      <Shield className="w-4 h-4 text-gray-600" />
      <span className="text-sm text-gray-600">Dados protegidos com criptografia</span>
    </div>
  )

  const RejectDialog = () => (
    <Dialog
      open={state.showRejectDialog}
      onOpenChange={(open) => setState((prev) => ({ ...prev, showRejectDialog: open }))}
    >
      <DialogContent className="max-w-md mx-auto rounded-3xl border-0 shadow-2xl bg-gradient-to-br from-white to-gray-50">
        <DialogHeader className="text-center pb-6">
          <div className="flex justify-center mb-4">
            <div className="bg-gradient-to-r from-[#074377] to-[#0066cc] rounded-2xl p-3 shadow-lg">
              <Image src="/images/locpay-logo.png" alt="LocPay" width={80} height={21} className="h-5 w-auto" />
            </div>
          </div>
          <div className="relative w-20 h-20 mx-auto mb-6">
            <div className="absolute inset-0 bg-gradient-to-r from-red-100 to-orange-100 rounded-full animate-pulse"></div>
            <div className="relative w-full h-full bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center border-4 border-white shadow-xl">
              <ThumbsDown className="h-10 w-10 text-white" />
            </div>
            <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
              <span className="text-white text-lg">😔</span>
            </div>
          </div>
          <DialogTitle className="text-2xl font-black text-gray-800 mb-3">Que pena!</DialogTitle>
          <DialogDescription className="text-gray-600 text-base leading-relaxed">
            Entendemos que nossa proposta não atendeu suas expectativas no momento.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6">
          <div className="relative p-6 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-2xl border border-blue-200 overflow-hidden">
            <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-xl"></div>
            <div className="relative">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-[#074377] to-[#0066cc] rounded-2xl flex items-center justify-center shadow-lg">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h4 className="font-black text-gray-800 text-lg">Não desista!</h4>
                  <p className="text-sm text-gray-600">Nossas condições mudam constantemente</p>
                </div>
              </div>
              <div className="space-y-3 mb-6">
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40">
                  <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-sm font-medium text-gray-700">Melhores taxas do mercado</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center">
                    <Sparkles className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-sm font-medium text-gray-700">Condições sempre atualizadas</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                    <HeartHandshake className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-sm font-medium text-gray-700">Equipe especializada</span>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  onClick={() => {
                    setState(INITIAL_STATE)
                  }}
                  className="bg-gradient-to-r from-[#074377] to-[#0066cc] hover:from-[#074377]/90 hover:to-[#0066cc]/90 text-white rounded-xl h-12 text-sm font-bold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Nova Simulação
                </Button>
                <Button
                  onClick={() =>
                    window.open(
                      "https://wa.me/5541999999999?text=Olá! Gostaria de falar sobre as condições da antecipação de aluguel.",
                      "_blank",
                    )
                  }
                  className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-xl h-12 text-sm font-bold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
                  WhatsApp
                </Button>
              </div>
            </div>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-4 border border-gray-200">
              <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                  <HeartHandshake className="w-3 h-3 text-white" />
                </div>
                <span className="font-medium">Estamos aqui para conseguir a melhor proposta para você</span>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )

  const LandingPage = () => {
    return (
      <div className="min-h-screen bg-[#074377] relative overflow-hidden">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-3xl"></div>
          <div className="absolute bottom-40 right-16 w-40 h-40 bg-white rounded-full blur-3xl"></div>
        </div>
        <div className="relative z-10 px-6 py-12 flex flex-col min-h-screen">
          <div className="flex justify-center mb-12">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <Image
                src="/images/locpay-logo.png"
                alt="LocPay"
                width={160}
                height={42}
                className="h-10 w-auto"
              />
            </div>
          </div>
          <div className="flex-1 flex flex-col justify-center text-center">
            <div className="mb-6">
              <h1 className="text-4xl font-black text-white mb-6 leading-tight">
                Antecipe Seus
                <br />
                <span className="bg-gradient-to-r from-blue-300 to-blue-100 bg-clip-text text-transparent">
                  Aluguéis
                </span>
                <span className="ml-2">🏠🏢</span>
              </h1>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 mb-10 max-w-sm mx-auto">
                <p className="text-lg text-white/90 font-medium">
                  Receba até <span className="font-bold text-blue-300">12 meses</span> de aluguel antecipado em{" "}
                  <span className="font-bold text-blue-300">1 hora</span>
                </p>
              </div>
            </div>
            <div className="space-y-3 mb-12 max-w-sm mx-auto">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-600 rounded-xl flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                      />
                    </svg>
                  </div>
                  <div className="text-left">
                    <p className="text-white font-semibold text-sm">+R$ 7M antecipados</p>
                    <p className="text-white/70 text-xs">Volume total processado</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-xl flex items-center justify-center">
                    <Clock className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-left">
                    <p className="text-white font-semibold text-sm">Aprovação em 1 hora</p>
                    <p className="text-white/70 text-xs">Processo 100% digital</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-purple-600 rounded-xl flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      />
                    </svg>
                  </div>
                  <div className="text-left">
                    <p className="text-white font-semibold text-sm">Melhores taxas do mercado</p>
                    <p className="text-white/70 text-xs">Condições exclusivas</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="mb-8">
              <div className="flex justify-center gap-1 mb-2">
                {[1, 2, 3, 4, 5].map((i) => (
                  <Star key={i} className="w-4 h-4 text-blue-300 fill-current" />
                ))}
              </div>
              <p className="text-white/80 text-sm font-medium">Mais de 10.000 clientes satisfeitos</p>
            </div>
          </div>
          <div className="space-y-4">
            <Button
              onClick={() => setState((prev) => ({ ...prev, step: 1 }))}
              className="w-full h-14 text-base font-semibold bg-white text-[#074377] hover:bg-gray-50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98]"
            >
              <Rocket className="w-5 h-5 mr-3" />
              Começar Antecipação
              <ArrowRight className="w-5 h-5 ml-3" />
            </Button>
            <div className="text-center">
              <Button
                onClick={() =>
                  window.open(
                    "https://wa.me/5541999999999?text=Olá! Gostaria de saber mais sobre a antecipação de aluguel.",
                    "_blank",
                  )
                }
                variant="ghost"
                className="text-white/80 hover:text-white hover:bg-white/10 text-sm border border-white/20 rounded-xl px-6 py-2 transition-all duration-300"
              >
                <Phone className="w-4 h-4 mr-2" />
                Falar com especialista
              </Button>
            </div>
            <div className="flex items-center justify-center gap-4 text-white/60 text-xs pt-4">
              <div className="flex items-center gap-1">
                <Shield className="w-3 h-3" />
                <span>SSL Seguro</span>
              </div>
              <div className="flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                <span>LGPD</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const Page1_Form = () => {
    const handleInputChange = (field: keyof FormData, value: string | boolean) =>
      setState((prev) => ({ ...prev, formData: { ...prev.formData, [field]: value }, error: "" }))

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0]
      if (file && file.type === "application/pdf") {
        handleInputChange("contratoFileName", file.name)
      } else if (file) {
        setState((prev) => ({ ...prev, error: "Por favor, envie um arquivo PDF." }))
      } else {
        handleInputChange("contratoFileName", "")
      }
    }

    const handleSubmit = () => {
      if (!state.formData.dataConsent) {
        setState((prev) => ({ ...prev, error: "Você deve aceitar o uso dos seus dados para continuar." }))
        return
      }
      startLoading(2000, 3500, "Analisando contrato com IA...", () =>
        setState((prev) => ({ ...prev, step: 2, extractedData: MOCK_EXTRACTED_DATA })),
      )
    }

    return (
      <div className="min-h-screen bg-[#074377] pb-24">
        <PageHeader />
        <div className="px-4">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">Receba Vários Meses de Aluguéis Agora! 🚀</h1>
            <p className="text-white/90 font-normal">
              Preencha os dados abaixo e receba até 12 meses de aluguéis futuros ainda hoje 💙
            </p>
          </div>
          <Card className="bg-white shadow-xl rounded-3xl border-0 mb-6">
            <CardContent className="space-y-6 p-6">
              <InputField
                icon={Smile}
                id="nomeCompleto"
                label="Nome Completo"
                placeholder="Digite seu nome completo"
                value={state.formData.nomeCompleto}
                onChange={(e) => handleInputChange("nomeCompleto", e.target.value)}
                inputRef={firstInputRef}
              />
              <InputField
                icon={CreditCard}
                id="cpf"
                label="CPF"
                placeholder="000.000.000-00"
                value={state.formData.cpf}
                onChange={(e) => handleInputChange("cpf", maskCPF(e.target.value))}
                maxLength={14}
              />
              <InputField
                icon={Smartphone}
                id="celular"
                label="Celular"
                placeholder="(11) 99999-9999"
                value={state.formData.celular}
                onChange={(e) => handleInputChange("celular", maskPhone(e.target.value))}
                maxLength={15}
              />
              <InputField
                icon={Mail}
                id="email"
                label="E-mail"
                placeholder="<EMAIL>"
                type="email"
                value={state.formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
              />
              <div className="space-y-2">
                <label htmlFor="mesesAntecipacao" className="block text-sm font-medium text-gray-700">
                  Meses para Antecipar <span className="text-gray-500">(máx. 12)</span>
                </label>
                <div className="relative">
                  <CalendarDays className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[#074377]" />
                  <Input
                    id="mesesAntecipacao"
                    type="number"
                    min="1"
                    max="12"
                    placeholder="Ex: 6"
                    value={state.formData.mesesAntecipacao}
                    onChange={(e) => {
                      const value = e.target.value
                      if (value === "" || (Number(value) >= 1 && Number(value) <= 12)) {
                        handleInputChange("mesesAntecipacao", value)
                      }
                    }}
                    className={`${commonInputClass} ${inputWithIconClass}`}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Contrato de Locação (PDF)</label>
                <label
                  htmlFor="contrato"
                  className={`flex flex-col items-center justify-center w-full h-20 border-2 border-dashed rounded-xl cursor-pointer transition-all duration-300 ${
                    state.formData.contratoFileName
                      ? "border-[#074377] bg-blue-50 text-[#074377]"
                      : "border-gray-300 bg-gray-50 text-gray-500 hover:border-gray-400"
                  }`}
                >
                  <Upload
                    className={`w-5 h-5 mb-1 ${state.formData.contratoFileName ? "text-[#074377]" : "text-gray-400"}`}
                  />
                  <span className="text-sm font-medium">
                    {state.formData.contratoFileName || "Clique para fazer upload do contrato"}
                  </span>
                  <span className="text-xs text-gray-400">Apenas PDF - até 20MB</span>
                </label>
                <Input id="contrato" type="file" accept=".pdf" onChange={handleFileChange} className="hidden" />
              </div>
              <div className="flex items-start space-x-3 p-4 bg-gray-50 border border-gray-200 rounded-xl">
                <Checkbox
                  id="dataConsent"
                  checked={state.formData.dataConsent}
                  onCheckedChange={(checked) => handleInputChange("dataConsent", Boolean(checked))}
                  className="mt-1 border-2 border-gray-400 data-[state=checked]:bg-[#074377] data-[state=checked]:border-[#074377] w-5 h-5"
                />
                <label htmlFor="dataConsent" className="text-sm text-gray-700 cursor-pointer">
                  Aceito o uso dos meus dados conforme a{" "}
                  <Button variant="link" className="p-0 h-auto text-[#074377] text-sm font-medium underline">
                    Política de Privacidade
                  </Button>
                  .
                </label>
              </div>
            </CardContent>
          </Card>
          {state.error && (
            <Alert variant="destructive" className="mb-6 bg-red-50 border-red-200 text-red-700 rounded-xl">
              <AlertTriangle className="h-5 w-5" />
              <AlertDescription>{state.error}</AlertDescription>
            </Alert>
          )}
          <Button onClick={handleSubmit} className={mainButtonClass}>
            ANALISAR CONTRATO
            <Zap className="w-5 h-5 ml-2" />
          </Button>
          <SecurityBadge />
        </div>
        <StepIndicator />
      </div>
    )
  }

  const Page2 = () => {
    const confirmData = () =>
      startLoading(2000, 3500, "Gerando sua proposta personalizada...", () =>
        setState((prev) => ({ ...prev, step: 3 })),
      )

    const handleIncorrectData = () => {
      setState((prev) => ({
        ...prev,
        step: 1,
        error:
          "Por favor, envie um contrato de locação correto ou entre em contato conosco pelo WhatsApp para assistência.",
      }))
    }

    return (
      <div className="min-h-screen bg-[#074377] pb-24">
        <PageHeader />
        <div className="px-4">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-white mb-2">Olá, {state.formData.nomeCompleto.split(" ")[0]}!</h1>
            <p className="text-white/90">
              Por favor, confirme se os dados do contrato de locação abaixo estão corretos.
            </p>
          </div>
          <Card className="bg-white shadow-xl rounded-3xl border-0 mb-6">
            <CardContent className="p-0">
              <div className="divide-y divide-gray-300">
                {Object.entries(state.extractedData).map(([key, value], index) => {
                  const icons = [Smile, Building, MapPin, DollarSign, Calendar, Award]
                  const Icon = icons[index % icons.length]
                  return (
                    <div key={key} className="p-6 flex items-center gap-4">
                      <div className={`w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center`}>
                        <Icon className={`h-5 w-5 text-gray-600`} />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm text-gray-500 font-medium mb-1">{key}</p>
                        <p className="font-bold text-[#074377] text-lg">{String(value)}</p>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
          <div className="space-y-4">
            <Button onClick={confirmData} className={greenButtonClass}>
              <CheckCircle2 className="w-5 h-5 mr-2" />
              DADOS CORRETOS, VER PROPOSTA
            </Button>
            <Button onClick={handleIncorrectData} className={redButtonClass}>
              <XCircle className="w-4 h-4 mr-2" />
              Dados incorretos, corrigir
            </Button>
            <div className="flex items-center justify-center gap-2 mt-4">
              <span className="text-white/80 text-sm">Precisa de ajuda?</span>
              <Button
                onClick={() =>
                  window.open(
                    "https://wa.me/5541999999999?text=Olá! Preciso de ajuda com a validação dos dados do contrato.",
                    "_blank",
                  )
                }
                variant="link"
                className="text-blue-300 hover:text-blue-200 text-sm font-medium underline p-0 h-auto"
              >
                <MessageCircle className="w-4 h-4 mr-1" />
                Fale no WhatsApp
              </Button>
            </div>
          </div>
        </div>
        <StepIndicator />
      </div>
    )
  }

  const Page3 = () => {
    const monthlyRent = 2800
    const totalRentValue = monthlyRent * state.proposalMonths
    const netValue = totalRentValue * 0.975

    const handleRejectProposal = () => {
      setState((prev) => ({ ...prev, showRejectDialog: true }))
    }

    return (
      <div className="min-h-screen bg-[#074377] pb-24">
        <PageHeader />
        <div className="px-4">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">Temos uma Proposta!🎉</h1>
            <p className="text-white/90">
              Sua antecipação foi <span className="font-bold">pré-aprovada</span>!
            </p>
          </div>
          <Card className="bg-white shadow-xl rounded-3xl border-0 mb-6">
            <CardContent className="space-y-6 p-6">
              <div className="text-center">
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-emerald-200 rounded-2xl p-6 mb-4">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Star className="h-6 w-6 text-emerald-600 fill-current" />
                    <span className="text-emerald-700 font-semibold">Proposta Especial</span>
                  </div>
                  <p className="text-4xl font-black text-emerald-800 mb-2">{formatCurrency(netValue)}</p>
                  <p className="text-emerald-600 font-medium">Valor líquido a receber</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-gradient-to-br from-slate-50 to-slate-100 rounded-xl border border-slate-200">
                  <div className="flex items-center gap-2 mb-2">
                    <DollarSign className="h-5 w-5 text-slate-600" />
                    <span className="text-sm text-slate-600 font-medium">Aluguel Mensal</span>
                  </div>
                  <div className="text-center">
                    <p className="text-xl font-bold text-[#074377]">{formatCurrency(monthlyRent)}</p>
                  </div>
                </div>
                <div className="p-4 bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-xl border border-indigo-200">
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="h-5 w-5 text-indigo-600" />
                    <span className="text-sm text-indigo-600 font-medium">Meses Antecipados</span>
                  </div>
                  <p className="text-xl font-bold text-center text-[#074377]">{state.proposalMonths} meses</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <div className="space-y-4">
            <Button
              onClick={() =>
                startLoading(1500, 2500, "Preparando confirmação...", () => setState((prev) => ({ ...prev, step: 4 })))
              }
              className={greenButtonClass}
            >
              <CheckCircle2 className="w-5 h-5 mr-2" />
              SIM, ACEITO A PROPOSTA!
            </Button>
            <Button onClick={handleRejectProposal} className={redButtonClass}>
              <ThumbsDown className="w-4 h-4 mr-2" />
              Não, recusar proposta
            </Button>
          </div>
        </div>
        <RejectDialog />
        <StepIndicator />
      </div>
    )
  }

  const Page4 = () => {
    const [pixKey, setPixKey] = useState("")
    const [termsAccepted, setTermsAccepted] = useState(false)

    const finalizar = () => {
      if (!pixKey.trim()) {
        setState((prev) => ({ ...prev, error: "Por favor, informe sua chave PIX." }))
        return;
      }
      if (!termsAccepted) {
        setState((prev) => ({ ...prev, error: "Você precisa aceitar os Termos de Cessão de Crédito." }))
        return;
      }
      startLoading(2000, 3000, "Enviando solicitação para análise final...", () =>
        setState((prev) => ({ ...prev, step: 5 })),
      )
    }

    const monthlyRent = 2800
    const netValue = monthlyRent * state.proposalMonths * 0.975

    return (
      <div className="min-h-screen bg-[#074377] pb-24">
        <PageHeader />
        <div className="px-4">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-white mb-2">Informe o seu PIX</h1>
            <p className="text-white/90">Confirme os detalhes finais da operação</p>
          </div>
          <div className="space-y-6 mb-6">
            <Card className="bg-white shadow-xl rounded-3xl border-2 border-[#074377]/20">
              <CardContent className="p-6">
                <InputField
                  id="pixKey"
                  label="Sua chave PIX"
                  placeholder="CPF, e-mail, telefone ou chave aleatória"
                  value={pixKey}
                  onChange={(e) => {
                    setPixKey(e.target.value)
                    if (state.error) setState(prev => ({ ...prev, error: ""}))
                  }}
                />
              </CardContent>
            </Card>

            <Card className="bg-white shadow-xl rounded-3xl border-0 overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-[#074377] to-[#00A7E1] text-white p-6">
                <CardTitle className="text-xl font-bold flex items-center gap-3">
                  <FileText className="h-6 w-6" />
                  Resumo da Operação
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="divide-y divide-gray-100">
                  <div className="p-6 flex items-center gap-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <MapPin className="h-6 w-6 text-[#074377]" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-500 font-medium">Imóvel</p>
                      <p className="font-bold text-[#074377] text-lg">{state.extractedData.Imóvel}</p>
                    </div>
                  </div>
                  <div className="p-6 flex items-center gap-4">
                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                      <DollarSign className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-500 font-medium">Valor do Aluguel</p>
                      <p className="font-bold text-[#074377] text-lg">{formatCurrency(monthlyRent)}</p>
                    </div>
                  </div>
                  <div className="p-6 flex items-center gap-4">
                    <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                      <Calendar className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-500 font-medium">Meses Antecipados</p>
                      <p className="font-bold text-[#074377] text-lg">{state.proposalMonths} meses</p>
                    </div>
                  </div>
                  <div className="p-6 flex items-center gap-4">
                    <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                      <Building className="h-6 w-6 text-orange-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-500 font-medium">Imobiliária</p>
                      <p className="font-bold text-[#074377] text-lg">{state.extractedData.Imobiliária}</p>
                    </div>
                  </div>
                  <div className="p-6 bg-gradient-to-r from-blue-50 to-green-50">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-gray-600 font-medium text-lg">Valor líquido a receber</p>
                        <p className="text-xs text-gray-500 mt-1">Valor que será depositado na sua conta</p>
                      </div>
                      <div className="text-right">
                        <p className="font-black text-[#074377] text-3xl">{formatCurrency(netValue)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <div className="flex items-start space-x-3 p-4 bg-white border border-gray-200 rounded-xl">
              <Checkbox
                id="terms"
                checked={termsAccepted}
                onCheckedChange={(checked) => {
                  setTermsAccepted(Boolean(checked))
                  if (state.error) setState((prev) => ({ ...prev, error: "" }))
                }}
                className="mt-1 border-2 border-gray-400 data-[state=checked]:bg-[#074377] data-[state=checked]:border-[#074377] w-5 h-5"
              />
              <label htmlFor="terms" className="text-sm text-gray-700 cursor-pointer">
                Li e aceito os{" "}
                <Button
                  variant="link"
                  className="p-0 h-auto text-[#074377] text-sm font-medium underline inline-flex items-center gap-1"
                >
                  <Eye className="w-3 h-3" />
                  Termos de Cessão de Crédito
                </Button>
              </label>
            </div>
          </div>
          {state.error && (
            <Alert variant="destructive" className="mb-6 bg-red-50 border-red-200 text-red-700 rounded-xl">
              <AlertTriangle className="h-5 w-5" />
              <AlertDescription>{state.error}</AlertDescription>
            </Alert>
          )}
          <Button onClick={finalizar} className={greenButtonClass}>
            <CheckCircle2 className="w-5 h-5 mr-2" />
            CONFIRMAR ANTECIPAÇÃO
          </Button>
        </div>
        <StepIndicator />
      </div>
    )
  }

  const SuccessScreen = () => (
    <div className="min-h-screen bg-[#074377] relative overflow-hidden">
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-3xl"></div>
        <div className="absolute bottom-40 right-16 w-40 h-40 bg-blue-300 rounded-full blur-3xl"></div>
      </div>
      <div className="relative z-10 flex flex-col items-center justify-center px-6 py-12 min-h-screen text-center">
        <div className="relative mb-8">
          <div className="w-32 h-32 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-2xl">
            <CheckCircle2 className="text-white h-16 w-16" />
          </div>
          <div className="absolute -top-2 -right-2 w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
            <Sparkles className="w-6 h-6 text-white" />
          </div>
        </div>
        <div className="space-y-6 mb-12">
          <h1 className="text-4xl font-black text-white">Solicitação Enviada! 🎉🏆</h1>
          <div className="max-w-md mx-auto space-y-4">
            <p className="text-xl text-white/90">
              Parabéns, <span className="font-bold text-blue-300">{state.formData.nomeCompleto.split(" ")[0]}</span>!
            </p>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
              <p className="text-white/90 leading-relaxed">
                Nossa equipe irá analisar sua solicitação. Uma vez aprovada, você receberá o{" "}
                <span className="font-semibold text-blue-300">contrato para assinatura pelo WhatsApp</span> e o PIX será
                enviado em até 12 horas úteis.
              </p>
            </div>
          </div>
        </div>
        <Card className="bg-white shadow-2xl rounded-3xl border-2 border-[#074377]/20 max-w-sm w-full mb-8 relative">
          <CardContent className="relative p-8 space-y-6">
            <div className="text-center">
              <div className="text-3xl mb-3">🤝</div>
              <h3 className="text-xl font-bold text-[#074377] border-b-2 border-[#074377]/10 pb-2">Próximos Passos</h3>
              <p className="text-sm text-gray-500 mt-2">Acompanhe o andamento da sua solicitação</p>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-4 p-3 bg-blue-50 rounded-xl">
                <div className="w-8 h-8 bg-gradient-to-r from-[#074377] to-[#0066cc] rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-sm font-bold">1</span>
                </div>
                <span className="text-gray-700 font-medium">Análise da equipe LocPay</span>
              </div>
              <div className="flex items-center gap-4 p-3 bg-blue-50 rounded-xl">
                <div className="w-8 h-8 bg-gradient-to-r from-[#074377] to-[#0066cc] rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-sm font-bold">2</span>
                </div>
                <span className="text-gray-700 font-medium">Contrato enviado por WhatsApp</span>
              </div>
              <div className="flex items-center gap-4 p-3 bg-green-50 rounded-xl border border-green-200">
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <CheckCircle2 className="w-4 h-4 text-white" />
                </div>
                <span className="text-gray-700 font-medium">PIX em até 12h após assinatura</span>
              </div>
            </div>
          </CardContent>
        </Card>
        <div className="space-y-4 w-full max-w-sm mb-8">
          <Button
            onClick={() =>
              window.open(
                "https://wa.me/5541999999999?text=Olá! Acabei de enviar minha solicitação de antecipação.",
                "_blank",
              )
            }
            className="w-full bg-green-600 hover:bg-green-700 text-white rounded-xl h-12 transition-all duration-300 transform hover:scale-[1.02]"
          >
            <MessageCircle className="w-5 h-5 mr-2" />
            Acompanhar pelo WhatsApp
          </Button>
          <Button
            onClick={() => {
              localStorage.removeItem("locpay_app_state_v1")
              setState(INITIAL_STATE)
            }}
            variant="ghost"
            className="w-full text-white/80 hover:text-white hover:bg-white/10 rounded-xl h-10 transition-all duration-300 border border-white/20"
          >
            Fazer nova solicitação
          </Button>
        </div>
        <div className="flex items-center justify-center gap-6 text-white text-sm font-medium">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
              <Shield className="w-3 h-3 text-white" />
            </div>
            <span>Dados Seguros</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
              <CheckCircle className="w-3 h-3 text-white" />
            </div>
            <span>Processo Confiável</span>
          </div>
        </div>
      </div>
    </div>
  )

  const renderStep = () => {
    switch (state.step) {
      case 0:
        return <LandingPage />
      case 1:
        return <Page1_Form />
      case 2:
        return <Page2 />
      case 3:
        return <Page3 />
      case 4:
        return <Page4 />
      case 5:
        return <SuccessScreen />
      default:
        return <LandingPage />
    }
  }

  return (
    <div className="w-full max-w-md mx-auto bg-white sm:rounded-3xl sm:shadow-2xl my-0 sm:my-4 font-sans overflow-hidden relative">
      {state.loading && <LoadingScreen />}
      {showQuickNav && <QuickNavigation />}
      <div className="min-h-screen">{renderStep()}</div>
      <Button
        onClick={() => setShowQuickNav(!showQuickNav)}
        className="fixed bottom-32 right-4 w-12 h-12 rounded-full bg-[#074377] hover:bg-[#074377]/90 text-white shadow-xl z-50 sm:hidden"
        size="icon"
      >
        <Settings2 className="w-5 h-5" />
      </Button>
    </div>
  )
}
