import { SetMetadata } from '@nestjs/common';
import { Throttle } from '@nestjs/throttler';

// Rate limiting para autenticação (mais restritivo)
export const AuthRateLimit = () =>
  Throttle({
    short: { ttl: 1000, limit: 1 }, // 1 request por segundo
    medium: { ttl: 60000, limit: 5 }, // 5 requests por minuto
    long: { ttl: 900000, limit: 10 }, // 10 requests por 15 minutos
  });

// Rate limiting para operações sensíveis (moderado)
export const SensitiveRateLimit = () =>
  Throttle({
    short: { ttl: 1000, limit: 2 }, // 2 requests por segundo
    medium: { ttl: 60000, limit: 10 }, // 10 requests por minuto
    long: { ttl: 300000, limit: 20 }, // 20 requests por 5 minutos
  });

// Rate limiting para uploads (específico)
export const UploadRateLimit = () =>
  Throttle({
    short: { ttl: 10000, limit: 3 }, // 3 uploads por 10 segundos
    medium: { ttl: 60000, limit: 10 }, // 10 uploads por minuto
    long: { ttl: 3600000, limit: 50 }, // 50 uploads por hora
  });

// Rate limiting padrão para APIs gerais
export const StandardRateLimit = () =>
  Throttle({
    short: { ttl: 1000, limit: 5 }, // 5 requests por segundo
    medium: { ttl: 60000, limit: 100 }, // 100 requests por minuto
    long: { ttl: 3600000, limit: 1000 }, // 1000 requests por hora
  });

// Decorator para pular rate limiting (para endpoints internos)
export const SkipThrottle = (skip = true) => SetMetadata('skipThrottle', skip);
