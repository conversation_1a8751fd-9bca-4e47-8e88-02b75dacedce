import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';

@Injectable()
export class RealEstateService {
  constructor(private readonly prisma: PrismaService) {}

  async findAll() {
    return this.prisma.realEstate.findMany({
      orderBy: { name: 'asc' },
    });
  }

  async findById(id: string) {
    return this.prisma.realEstate.findUnique({
      where: { id },
    });
  }

  async create(data: { name: string; cnpj: string }) {
    return this.prisma.realEstate.create({
      data,
    });
  }
}
