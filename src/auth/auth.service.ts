import {
  Injectable,
  BadRequestException,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma.service';
import { N8nService } from '../integrations/n8n/n8n.service';
import { LoginDto } from './dto/login.dto';
import { VerifyCodeDto } from './dto/verify-code.dto';
import { User } from '@prisma/client';
import { ValidationUtils } from '../common/utils/validation.utils';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly n8nService: N8nService,
    private readonly jwtService: JwtService,
  ) {}

  async loginOrCreateUser(
    dto: LoginDto,
  ): Promise<{ user: Omit<User, 'updatedAt'>; codeSent: boolean }> {
    // Validação e sanitização do CPF
    const sanitizedCpf = ValidationUtils.sanitizeCPF(dto.cpf);
    if (!ValidationUtils.validateCPF(sanitizedCpf)) {
      throw new BadRequestException('CPF inválido');
    }

    // Validação do telefone se fornecido
    if (dto.phone && !ValidationUtils.validatePhone(dto.phone)) {
      throw new BadRequestException('Telefone inválido');
    }

    const sanitizedPhone = dto.phone
      ? ValidationUtils.sanitizePhone(dto.phone)
      : null;

    try {
      let user = await this.prisma.user.findUnique({
        where: { cpf: sanitizedCpf },
      });

      if (!user) {
        if (!dto.phone) {
          throw new BadRequestException(
            'Telefone é obrigatório para novos usuários',
          );
        }

        this.logger.log(`Criando novo usuário com CPF: ${sanitizedCpf}`);
        user = await this.prisma.user.create({
          data: {
            name: dto.name.trim(),
            cpf: sanitizedCpf,
            phone: sanitizedPhone,
          },
        });
      } else if (dto.phone && user.phone !== sanitizedPhone) {
        // Atualiza telefone se foi fornecido e é diferente
        this.logger.log(`Atualizando telefone do usuário: ${user.id}`);
        user = await this.prisma.user.update({
          where: { id: user.id },
          data: { phone: sanitizedPhone },
        });
      }

      if (!user.phone) {
        throw new BadRequestException(
          'Usuário sem telefone cadastrado. Forneça um telefone válido.',
        );
      }

      await this.sendLoginCode(user);

      // Remove dados sensíveis da resposta
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { updatedAt: _, ...userResponse } = user;
      return { user: userResponse, codeSent: true };
    } catch (error) {
      this.logger.error(`Erro ao fazer login/criar usuário:`, error);
      throw error;
    }
  }

  async sendLoginCode(user: User): Promise<void> {
    if (!user.phone) {
      throw new BadRequestException('Usuário sem telefone cadastrado');
    }

    try {
      // Invalidar códigos anteriores não utilizados
      await this.prisma.loginCode.deleteMany({
        where: {
          userId: user.id,
          expiresAt: { gt: new Date() },
        },
      });

      const code = ValidationUtils.generateRandomCode(6);
      const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutos

      await this.prisma.loginCode.create({
        data: {
          userId: user.id,
          code,
          expiresAt,
        },
      });

      this.logger.log(`Código de login gerado para usuário: ${user.id}`);
      await this.n8nService.sendWhatsappCode(user.phone, code);
    } catch (error) {
      this.logger.error(`Erro ao enviar código de login:`, error);
      throw new BadRequestException('Erro ao enviar código de verificação');
    }
  }

  async verifyCode(
    dto: VerifyCodeDto,
  ): Promise<{ user: User; accessToken: string }> {
    const sanitizedCpf = ValidationUtils.sanitizeCPF(dto.cpf);

    if (!ValidationUtils.validateCPF(sanitizedCpf)) {
      throw new BadRequestException('CPF inválido');
    }

    if (dto.code.length !== 6 || !/^\d{6}$/.test(dto.code)) {
      throw new BadRequestException('Código deve ter 6 dígitos');
    }

    try {
      const loginCode = await this.prisma.loginCode.findFirst({
        where: {
          code: dto.code,
          user: { cpf: sanitizedCpf },
          expiresAt: { gt: new Date() },
        },
        include: { user: true },
        orderBy: { createdAt: 'desc' },
      });

      if (!loginCode) {
        this.logger.warn(
          `Tentativa de login com código inválido para CPF: ${sanitizedCpf}`,
        );
        throw new UnauthorizedException('Código inválido ou expirado');
      }

      // Remove o código usado
      await this.prisma.loginCode.delete({
        where: { id: loginCode.id },
      });

      // Gera JWT token
      const payload = { sub: loginCode.user.id, cpf: loginCode.user.cpf };
      const accessToken = this.jwtService.sign(payload);

      this.logger.log(
        `Login realizado com sucesso para usuário: ${loginCode.user.id}`,
      );

      return {
        user: loginCode.user,
        accessToken,
      };
    } catch (error) {
      this.logger.error(`Erro ao verificar código:`, error);
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException('Erro ao verificar código');
    }
  }

  async resendCode(cpf: string): Promise<{ codeSent: boolean }> {
    const sanitizedCpf = ValidationUtils.sanitizeCPF(cpf);

    if (!ValidationUtils.validateCPF(sanitizedCpf)) {
      throw new BadRequestException('CPF inválido');
    }

    try {
      const user = await this.prisma.user.findUnique({
        where: { cpf: sanitizedCpf },
      });

      if (!user) {
        throw new UnauthorizedException('Usuário não encontrado');
      }

      await this.sendLoginCode(user);
      return { codeSent: true };
    } catch (error) {
      this.logger.error(`Erro ao reenviar código:`, error);
      throw error;
    }
  }
}
