import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { QueueService } from './queue.service';

@Processor('n8n-responses')
export class N8nQueueProcessor extends WorkerHost {
  private readonly logger = new Logger(N8nQueueProcessor.name);

  constructor(private queueService: QueueService) {
    super();
  }

  async process(job: Job<any>): Promise<any> {
    const { name } = job;

    switch (name) {
      case 'monitor-job':
        return this.handleJobMonitoring(job);
      case 'process-n8n-response':
        return this.handleN8nResponse(job);
      case 'check-timeouts':
        return this.handleTimeoutCheck();
      default:
        this.logger.warn(`Job desconhecido: ${name}`);
    }
  }

  async handleJobMonitoring(job: Job<{ jobId: string }>) {
    const { jobId } = job.data;
    this.logger.debug(`Monitorando job: ${jobId}`);

    // Verificar se o job ainda está pendente
    const jobData = await this.queueService.getJob(jobId);

    if (!jobData) {
      this.logger.warn(`Job ${jobId} não encontrado no banco`);
      return;
    }

    if (jobData.status === 'pending') {
      // Se ainda está pendente, verificar se não passou do timeout
      const timeoutMinutes = 15;
      const timeoutDate = new Date(Date.now() - timeoutMinutes * 60 * 1000);

      if (jobData.createdAt < timeoutDate) {
        this.logger.warn(`Job ${jobId} expirou por timeout`);
        await this.queueService.processN8nResponse({
          jobId,
          success: false,
          error: 'Timeout - Job não processado pelo N8N em tempo hábil',
        });
      } else {
        // Ainda dentro do prazo, reagendar verificação
        throw new Error('Job ainda pendente, reagendando verificação');
      }
    }

    this.logger.debug(`Job ${jobId} finalizado com status: ${jobData.status}`);
  }

  async handleN8nResponse(job: Job<any>) {
    const responseData = job.data;
    this.logger.log(
      `Processando resposta do N8N para job: ${responseData.jobId}`,
    );

    try {
      await this.queueService.processN8nResponse(responseData);
    } catch (error) {
      this.logger.error(`Erro ao processar resposta do N8N:`, error);
      throw error;
    }
  }

  async handleTimeoutCheck() {
    this.logger.debug('Verificando jobs com timeout');
    await this.queueService.checkPendingJobs();
  }
}
