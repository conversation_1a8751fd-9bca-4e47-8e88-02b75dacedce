import {
  Injectable,
  Logger,
  OnModuleInit,
  OnModuleDestroy,
} from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import Redis from 'ioredis';
import { QueueService, N8nResponse } from './queue.service';

@Injectable()
export class N8nPollingService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(N8nPollingService.name);
  private readonly redis: Redis;
  private readonly responseQueueKey = 'n8n:responses';
  private pollingInterval: NodeJS.Timeout | null = null;
  private isPolling = false;
  private currentPollingSpeed = 100; // 100ms padrão
  private readonly normalSpeed = 100; // 100ms
  private readonly overloadSpeed = 50; // 50ms para overload

  constructor(
    @InjectQueue('n8n-responses') private n8nQueue: Queue,
    private queueService: QueueService,
  ) {
    // Criar conexão Redis dedicada para polling
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      username: process.env.REDIS_USERNAME,
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_POLLING_DB || '3'), // DB dedicado para polling
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    });

    this.redis.on('connect', () => {
      this.logger.log('Conectado ao Redis para polling');
    });

    this.redis.on('error', (error) => {
      this.logger.error('Erro na conexão Redis:', error);
    });
  }

  /**
   * Inicializar polling ao iniciar o módulo
   */
  onModuleInit() {
    this.startPolling();
  }

  /**
   * Parar polling ao destruir o módulo
   */
  onModuleDestroy() {
    this.stopPolling();
    this.redis.disconnect();
  }

  /**
   * Iniciar o polling em alta frequência
   */
  private startPolling() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
    }

    this.pollingInterval = setInterval(async () => {
      if (!this.isPolling) {
        this.isPolling = true;
        try {
          await this.performPolling();
        } catch (error) {
          this.logger.error('Erro no polling:', error);
        } finally {
          this.isPolling = false;
        }
      }
    }, this.currentPollingSpeed);

    this.logger.log(
      `Polling iniciado com intervalo de ${this.currentPollingSpeed}ms`,
    );
  }

  /**
   * Parar o polling
   */
  private stopPolling() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
    this.logger.log('Polling parado');
  }

  /**
   * Ajustar velocidade do polling baseado na carga
   */
  private async adjustPollingSpeed() {
    const pendingJobs = await this.queueService.getJobsByStatus('pending');
    const newSpeed =
      pendingJobs.length > 5 ? this.overloadSpeed : this.normalSpeed;

    if (newSpeed !== this.currentPollingSpeed) {
      this.currentPollingSpeed = newSpeed;
      this.logger.debug(
        `Ajustando velocidade do polling para ${newSpeed}ms (${pendingJobs.length} jobs pendentes)`,
      );
      this.startPolling(); // Reiniciar com nova velocidade
    }
  }

  /**
   * Realizar uma operação de polling
   */
  private async performPolling() {
    try {
      // Usar BRPOP sem timeout para não bloquear
      const result = await this.redis.brpop(this.responseQueueKey, 0.01); // 10ms timeout

      if (result) {
        const [, responseStr] = result;
        const response: N8nResponse = JSON.parse(responseStr);

        this.logger.debug(`Resposta recebida do N8N: ${response.jobId}`);

        // Adicionar à queue Bull para processamento
        await this.n8nQueue.add('process-n8n-response', response, {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        });
      }
    } catch (error) {
      if (
        error.message !== 'Connection is closed.' &&
        !error.message.includes('timeout')
      ) {
        this.logger.error('Erro no polling de respostas do N8N:', error);
      }
    }
  }

  /**
   * Verificação de ajuste de velocidade a cada 5 segundos
   */
  @Cron('*/5 * * * * *')
  async checkAndAdjustPollingSpeed() {
    await this.adjustPollingSpeed();
  }

  /**
   * Verificação de timeout a cada 5 minutos
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async scheduleTimeoutCheck() {
    await this.n8nQueue.add(
      'check-timeouts',
      {},
      {
        attempts: 1,
      },
    );
  }

  /**
   * Método para testar conectividade
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.redis.ping();
      return true;
    } catch (error) {
      this.logger.error('Falha no teste de conexão Redis:', error);
      return false;
    }
  }

  /**
   * Método para adicionar resposta manualmente (para testes)
   */
  async addTestResponse(response: N8nResponse): Promise<void> {
    await this.redis.lpush(this.responseQueueKey, JSON.stringify(response));
    this.logger.log(`Resposta de teste adicionada: ${response.jobId}`);
  }

  /**
   * Obter tamanho da queue de respostas
   */
  async getResponseQueueSize(): Promise<number> {
    return await this.redis.llen(this.responseQueueKey);
  }

  /**
   * Obter estatísticas do polling
   */
  getPollingStats() {
    return {
      isActive: this.pollingInterval !== null,
      currentSpeed: this.currentPollingSpeed,
      normalSpeed: this.normalSpeed,
      overloadSpeed: this.overloadSpeed,
      isPolling: this.isPolling,
    };
  }

  /**
   * Forçar ajuste da velocidade do polling
   */
  async forceSpeedAdjustment() {
    await this.adjustPollingSpeed();
  }

  /**
   * Pausar polling temporariamente
   */
  pausePolling() {
    this.stopPolling();
    this.logger.log('Polling pausado manualmente');
  }

  /**
   * Retomar polling
   */
  resumePolling() {
    this.startPolling();
    this.logger.log('Polling retomado manualmente');
  }
}
