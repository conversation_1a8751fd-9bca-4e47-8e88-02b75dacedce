import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { QueueService } from './queue.service';
import { N8nPollingService } from './n8n-polling.service';
import { Public } from '../common/decorators/public.decorator';
import { UploadRateLimit } from '../rate-limit/rate-limit.decorator';

export class N8nWebhookDto {
  jobId: string;
  success: boolean;
  data?: any;
  error?: string;
}

@Controller('webhooks/n8n')
@Public() // Webhook do N8N não precisa de autenticação
export class N8nWebhookController {
  private readonly logger = new Logger(N8nWebhookController.name);

  constructor(
    private queueService: QueueService,
    private pollingService: N8nPollingService,
  ) {}

  /**
   * Webhook para receber respostas diretas do N8N
   * Alternativa ao sistema de polling Redis
   */
  @Post('response')
  @HttpCode(HttpStatus.OK)
  @UploadRateLimit() // Rate limit para uploads/webhooks
  async receiveN8nResponse(@Body() response: N8nWebhookDto) {
    this.logger.log(`Webhook recebido do N8N para job: ${response.jobId}`);

    try {
      // Processar a resposta
      await this.queueService.processN8nResponse(response);

      return {
        success: true,
        message: 'Resposta processada com sucesso',
        jobId: response.jobId,
      };
    } catch (error) {
      this.logger.error(`Erro ao processar webhook do N8N:`, error);
      return {
        success: false,
        message: 'Erro ao processar resposta',
        error: error.message,
      };
    }
  }

  /**
   * Endpoint para adicionar resposta na queue Redis (para testes)
   */
  @Post('queue-response')
  @HttpCode(HttpStatus.OK)
  @UploadRateLimit()
  async addResponseToQueue(@Body() response: N8nWebhookDto) {
    this.logger.log(`Adicionando resposta à queue: ${response.jobId}`);

    try {
      await this.pollingService.addTestResponse(response);

      return {
        success: true,
        message: 'Resposta adicionada à queue',
        jobId: response.jobId,
        queueSize: await this.pollingService.getResponseQueueSize(),
      };
    } catch (error) {
      this.logger.error(`Erro ao adicionar à queue:`, error);
      return {
        success: false,
        message: 'Erro ao adicionar resposta à queue',
        error: error.message,
      };
    }
  }
}
