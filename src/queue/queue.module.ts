import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ScheduleModule } from '@nestjs/schedule';
import { QueueService } from './queue.service';
import { N8nQueueProcessor } from './n8n-queue.processor';
import { N8nPollingService } from './n8n-polling.service';
import { N8nWebhookController } from './n8n-webhook.controller';
import { PrismaService } from '../prisma.service';

@Module({
  imports: [
    ScheduleModule.forRoot(), // Para os cron jobs
    BullModule.forRoot({
      connection: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        username: process.env.REDIS_USERNAME,
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_QUEUE_DB || '2'), // DB diferente para queues
      },
    }),
    BullModule.registerQueue({
      name: 'n8n-responses',
    }),
  ],
  controllers: [N8nWebhookController],
  providers: [
    QueueService,
    N8nQueueProcessor,
    N8nPollingService,
    PrismaService,
  ],
  exports: [QueueService, N8nPollingService],
})
export class QueueModule {}
