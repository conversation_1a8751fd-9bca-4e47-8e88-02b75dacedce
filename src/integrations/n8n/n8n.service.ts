import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';

@Injectable()
export class N8nService {
  private readonly logger = new Logger(N8nService.name);
  private readonly baseWebhookUrl = process.env.N8N_WEBHOOK_URL;

  constructor() {
    if (!this.baseWebhookUrl) {
      this.logger.error('N8N_WEBHOOK_URL não configurada');
    }
  }

  async sendWhatsappCode(phone: string, code: string): Promise<void> {
    if (!this.baseWebhookUrl) {
      throw new InternalServerErrorException('N8N webhook URL não configurada');
    }

    try {
      this.logger.log(
        `Enviando código WhatsApp para: ${phone.substring(0, 4)}****`,
      );

      const response = await axios.post(
        `${this.baseWebhookUrl}/whatsapp-code`,
        {
          phone,
          code,
          timestamp: new Date().toISOString(),
        },
        {
          timeout: 10000,
          headers: { 'Content-Type': 'application/json' },
        },
      );

      if (response.status !== 200) {
        throw new Error(`N8N retornou status ${response.status}`);
      }

      this.logger.log('Código WhatsApp enviado com sucesso');
    } catch (error) {
      this.logger.error('Falha ao enviar código via N8N:', error);
      throw new InternalServerErrorException(
        'Falha ao enviar código de verificação. Tente novamente.',
      );
    }
  }

  async sendContractForExtraction(
    contractPdf: Express.Multer.File,
  ): Promise<any> {
    if (!this.baseWebhookUrl) {
      throw new InternalServerErrorException('N8N webhook URL não configurada');
    }

    try {
      this.logger.log(
        `Enviando contrato para extração: ${contractPdf.originalname}`,
      );

      const formData = new FormData();
      formData.append(
        'file',
        new Blob([contractPdf.buffer]),
        contractPdf.originalname,
      );
      formData.append('mimeType', contractPdf.mimetype);
      formData.append('timestamp', new Date().toISOString());

      const response: AxiosResponse = await axios.post(
        `${this.baseWebhookUrl}/extract-contract`,
        formData,
        {
          timeout: 60000, // 60 segundos para extração
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );

      if (!response.data) {
        throw new Error('Resposta vazia do N8N');
      }

      this.logger.log('Extração de contrato concluída com sucesso');
      return response.data;
    } catch (error) {
      this.logger.error('Falha ao extrair dados do contrato via N8N:', error);
      throw new InternalServerErrorException(
        'Falha ao processar contrato. Verifique se o arquivo está legível.',
      );
    }
  }

  async requestProposal(data: {
    operationId: string;
    rentAmount: number;
    monthsToAdvance: number;
    realEstateId: string;
    userId: string;
    extractedData?: any;
  }): Promise<any> {
    if (!this.baseWebhookUrl) {
      throw new InternalServerErrorException('N8N webhook URL não configurada');
    }

    try {
      this.logger.log(
        `Solicitando proposta para operação: ${data.operationId}`,
      );

      const response: AxiosResponse = await axios.post(
        `${this.baseWebhookUrl}/request-proposal`,
        {
          ...data,
          timestamp: new Date().toISOString(),
        },
        {
          timeout: 30000, // 30 segundos para proposta
          headers: { 'Content-Type': 'application/json' },
        },
      );

      if (!response.data) {
        throw new Error('Resposta vazia do N8N para proposta');
      }

      // Validação da resposta da proposta
      const proposal = response.data;
      if (
        !proposal.proposalAmount ||
        !proposal.monthlyRentOffer ||
        !proposal.proposedMonths
      ) {
        throw new Error('Dados da proposta incompletos');
      }

      this.logger.log(`Proposta recebida para operação: ${data.operationId}`);
      return proposal;
    } catch (error) {
      this.logger.error('Falha ao solicitar proposta via N8N:', error);
      throw new InternalServerErrorException(
        'Falha ao gerar proposta. Tente novamente mais tarde.',
      );
    }
  }

  async notifyStatusChange(
    operationId: string,
    newStatus: string,
    additionalData?: any,
  ): Promise<void> {
    if (!this.baseWebhookUrl) {
      this.logger.warn(
        'N8N webhook URL não configurada - notificação não enviada',
      );
      return;
    }

    try {
      this.logger.log(
        `Notificando mudança de status: ${operationId} -> ${newStatus}`,
      );

      await axios.post(
        `${this.baseWebhookUrl}/status-notification`,
        {
          operationId,
          status: newStatus,
          timestamp: new Date().toISOString(),
          ...additionalData,
        },
        {
          timeout: 10000,
          headers: { 'Content-Type': 'application/json' },
        },
      );

      this.logger.log('Notificação de status enviada com sucesso');
    } catch (error) {
      // Não lançar erro aqui para não quebrar o fluxo principal
      this.logger.error('Falha ao notificar mudança de status via N8N:', error);
    }
  }

  async sendDocumentNotification(
    operationId: string,
    documentType: string,
    documentUrl: string,
  ): Promise<void> {
    if (!this.baseWebhookUrl) {
      this.logger.warn(
        'N8N webhook URL não configurada - notificação não enviada',
      );
      return;
    }

    try {
      this.logger.log(
        `Notificando novo documento: ${operationId} -> ${documentType}`,
      );

      await axios.post(
        `${this.baseWebhookUrl}/document-notification`,
        {
          operationId,
          documentType,
          documentUrl,
          timestamp: new Date().toISOString(),
        },
        {
          timeout: 10000,
          headers: { 'Content-Type': 'application/json' },
        },
      );

      this.logger.log('Notificação de documento enviada com sucesso');
    } catch (error) {
      // Não lançar erro aqui para não quebrar o fluxo principal
      this.logger.error('Falha ao notificar documento via N8N:', error);
    }
  }
}
