import { SetMetadata } from '@nestjs/common';

export const CACHE_KEY_METADATA = 'cache_key';
export const CACHE_TTL_METADATA = 'cache_ttl';

/**
 * Decorator para definir cache em métodos
 * @param key Chave do cache (pode incluir parâmetros como {id})
 * @param ttl Tempo de vida em segundos (opcional)
 */
export const CacheKey = (key: string, ttl?: number) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    SetMetadata(CACHE_KEY_METADATA, key)(target, propertyKey, descriptor);
    if (ttl) {
      SetMetadata(CACHE_TTL_METADATA, ttl)(target, propertyKey, descriptor);
    }
    return descriptor;
  };
};

/**
 * Decorator para invalidar cache
 * @param keys Array de chaves para invalidar
 */
export const CacheEvict = (keys: string[]) => {
  return SetMetadata('cache_evict', keys);
};
