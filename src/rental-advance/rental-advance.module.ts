import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { RentalAdvanceService } from './rental-advance.service';
import { RentalAdvanceController } from './rental-advance.controller';
import { PrismaService } from '../prisma.service';
import { N8nService } from '../integrations/n8n/n8n.service';
import { DriveService } from '../integrations/drive/drive.service';
import { CacheService } from '../cache/cache.service';
import { QueueModule } from '../queue/queue.module';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'locpay-secret-key',
      signOptions: { expiresIn: '24h' },
    }),
    QueueModule,
  ],
  controllers: [RentalAdvanceController],
  providers: [
    RentalAdvanceService,
    PrismaService,
    N8nService,
    DriveService,
    CacheService,
    JwtAuthGuard,
  ],
})
export class RentalAdvanceModule {}
