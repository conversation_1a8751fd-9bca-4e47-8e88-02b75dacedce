import { 
  IsString, 
  IsNotEmpty, 
  IsOptional, 
  IsUUID,
  IsDateString,
  MaxLength,
  IsObject 
} from 'class-validator';

export class ExtractedContractDataDto {
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Endereço muito longo' })
  propertyAddress?: string;

  @IsOptional()
  @IsString()
  @MaxLength(200, { message: 'Nome do locador muito longo' })
  landlordName?: string;

  @IsOptional()
  @IsString()
  @MaxLength(200, { message: 'Nome do locatário muito longo' })
  tenantName?: string;

  @IsOptional()
  @IsString()
  @MaxLength(20, { message: 'Documento do locador inválido' })
  landlordDocument?: string;

  @IsOptional()
  @IsString()
  @MaxLength(20, { message: 'Documento do locatário inválido' })
  tenantDocument?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'Tipo de garantia muito longo' })
  rentalGuarantee?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'Prazo do contrato muito longo' })
  contractTerm?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Data de início inválida' })
  startDate?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Data de término inválida' })
  endDate?: string;

  @IsOptional()
  @IsString()
  @MaxLength(50, { message: 'Número da matrícula muito longo' })
  propertyRegistry?: string;
}

export class ConfirmExtractedDataDto {
  @IsString({ message: 'ID da operação é obrigatório' })
  @IsNotEmpty({ message: 'ID da operação não pode estar vazio' })
  @IsUUID('4', { message: 'ID da operação deve ser um UUID válido' })
  operationId: string;

  @IsObject({ message: 'Dados extraídos devem ser um objeto válido' })
  extractedData: ExtractedContractDataDto;
}
