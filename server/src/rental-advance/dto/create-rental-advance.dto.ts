import { 
  IsString, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>Int, 
  <PERSON>, 
  Min, 
  IsOptional, 
  IsUUID,
  IsDecimal
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateRentalAdvanceDto {
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Valor do aluguel deve ser um número válido' })
  @Min(100, { message: 'Valor do aluguel deve ser de pelo menos R$ 100,00' })
  @Type(() => Number)
  rentAmount: number;

  @IsInt({ message: 'Quantidade de meses deve ser um número inteiro' })
  @Min(1, { message: 'Deve antecipar pelo menos 1 mês' })
  @Max(12, { message: 'Máximo de 12 meses podem ser antecipados' })
  @Type(() => Number)
  monthsToAdvance: number;

  @IsString({ message: 'ID da imobiliária é obrigatório' })
  @IsUUID('4', { message: 'ID da imobiliária deve ser um UUID válido' })
  realEstateId: string;

  @IsOptional()
  @IsString()
  contractPdfUrl?: string;
}
