import { 
  IsString, 
  <PERSON><PERSON><PERSON>E<PERSON><PERSON>, 
  <PERSON>In, 
  <PERSON>UUI<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Matches,
  IsEmail,
  IsOptional
} from 'class-validator';
import { PixKeyType } from '../enums/rental-status.enum';

export class FinalConfirmationDto {
  @IsString({ message: 'ID da operação é obrigatório' })
  @IsNotEmpty({ message: 'ID da operação não pode estar vazio' })
  @IsUUID('4', { message: 'ID da operação deve ser um UUID válido' })
  operationId: string;

  @IsString({ message: 'Chave PIX é obrigatória' })
  @IsNotEmpty({ message: 'Chave PIX não pode estar vazia' })
  @MaxLength(255, { message: 'Chave PIX muito longa' })
  pixKey: string;

  @IsString({ message: 'Tipo da chave PIX é obrigatório' })
  @IsIn([PixKeyType.CPF, PixKeyType.EMAIL, PixKeyType.PHONE, PixKeyType.RANDOM], {
    message: 'Tipo de chave PIX inválido'
  })
  pixKeyType: PixKeyType;

  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Observações muito longas' })
  notes?: string;

  // Validações específicas por tipo de chave PIX serão feitas no service
}
