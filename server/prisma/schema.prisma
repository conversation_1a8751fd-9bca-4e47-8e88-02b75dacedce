generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  name      String
  cpf       String   @unique @db.VarChar(11)
  phone     String?  @db.<PERSON>ar<PERSON>har(15)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relacionamentos
  loginCodes            LoginCode[]
  rentalAdvanceRequests RentalAdvanceRequest[]

  @@index([cpf])
  @@map("User")
}

model LoginCode {
  id        String   @id @default(cuid())
  userId    String
  code      String   @db.VarChar(6)
  expiresAt DateTime
  createdAt DateTime @default(now())

  // Relacionamentos
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([code, expiresAt])
  @@map("LoginCode")
}

model RealEstate {
  id        String   @id @default(cuid())
  name      String
  cnpj      String   @unique @db.<PERSON>ar<PERSON>har(14)
  createdAt DateTime @default(now())

  // Relacionamentos
  rentalAdvanceRequests RentalAdvanceRequest[]

  @@index([cnpj])
  @@map("RealEstate")
}

model RentalAdvanceRequest {
  id               String   @id @default(cuid())
  userId           String
  rentAmount       Decimal  @db.Decimal(12, 2)
  monthsToAdvance  Int      @db.SmallInt
  realEstateId     String?
  contractPdfUrl   String?  @db.Text
  currentStatus    String   @default("created")
  proposalAmount   Decimal? @db.Decimal(12, 2)
  monthlyRentOffer Decimal? @db.Decimal(12, 2)
  proposedMonths   Int?     @db.SmallInt
  pixKey           String?  @db.VarChar(255)
  identityDocUrl   String?  @db.Text
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relacionamentos
  user         User                     @relation(fields: [userId], references: [id], onDelete: Cascade)
  realEstate   RealEstate?              @relation(fields: [realEstateId], references: [id], onDelete: SetNull)
  contractData RentalContractData?
  statusLogs   RentalRequestStatusLog[]

  @@index([userId])
  @@index([currentStatus])
  @@index([createdAt])
  @@index([realEstateId])
  @@map("RentalAdvanceRequest")
}

model RentalContractData {
  id              String   @id @default(cuid())
  rentalRequestId String   @unique
  
  // Dados extraídos do contrato
  propertyAddress    String?  // Endereço do imóvel
  landlordName       String?  // Nome locador
  tenantName         String?  // Nome locatário  
  landlordDocument   String?  // CPF/CNPJ locador
  tenantDocument     String?  // CPF/CNPJ locatário
  rentalGuarantee    String?  // Garantia locatícia
  contractTerm       String?  // Prazo do contrato
  startDate          DateTime? // Data de início
  endDate            DateTime? // Data de término  
  propertyRegistry   String?  // Matrícula
  
  // Dados brutos para backup
  extractedData   Json?
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relacionamentos
  rentalRequest RentalAdvanceRequest @relation(fields: [rentalRequestId], references: [id], onDelete: Cascade)

  @@index([rentalRequestId])
  @@index([landlordDocument])
  @@index([tenantDocument])
  @@map("RentalContractData")
}

model RentalRequestStatusLog {
  id              String   @id @default(cuid())
  rentalRequestId String
  status          String
  createdAt       DateTime @default(now())

  // Relacionamentos  
  rentalRequest RentalAdvanceRequest @relation(fields: [rentalRequestId], references: [id], onDelete: Cascade)

  @@index([rentalRequestId])
  @@index([status])
  @@index([createdAt])
  @@map("RentalRequestStatusLog")
}

model N8nJobQueue {
  id              String   @id @default(cuid())
  jobId           String   @unique // ID único do job no N8N
  type            String   // Tipo: 'pdf_extraction', 'proposal_generation', etc.
  status          String   @default("pending") // pending, processing, completed, failed
  requestData     Json     // Dados enviados para o N8N
  responseData    Json?    // Resposta recebida do N8N
  relatedEntityId String?  // ID da entidade relacionada (RentalAdvanceRequest, etc.)
  entityType      String?  // Tipo da entidade: 'rental_request', etc.
  retryCount      Int      @default(0)
  maxRetries      Int      @default(3)
  errorMessage    String?  @db.Text
  processedAt     DateTime?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([jobId])
  @@index([status])
  @@index([type])
  @@index([relatedEntityId, entityType])
  @@index([createdAt])
  @@map("N8nJobQueue")
}
